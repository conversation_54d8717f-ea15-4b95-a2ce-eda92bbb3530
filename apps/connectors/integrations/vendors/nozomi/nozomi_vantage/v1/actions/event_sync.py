import logging
from datetime import datetime, timezone
from typing import Generator, List, Optional

import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.bookmarks import (
    NozomiVantageV1EventSyncBookmark,
)

logger = logging.getLogger(__name__)


def map_nozomi_severity_to_ocsf(nozomi_severity: int, is_incident: bool = False) -> ocsf.Severity:
    """
    Map Nozomi Vantage severity (0-10 scale) to OCSF severity (0-6 scale).

    Args:
        nozomi_severity: Nozomi severity value (0-10)
        is_incident: Whether the alert is marked as an incident (overrides to Critical)

    Returns:
        OCSF Severity enum value
    """
    # Override to Critical if marked as incident
    if is_incident:
        return ocsf.Severity.CRITICAL

    # Map Nozomi 0-10 scale to OCSF 0-6 scale
    if nozomi_severity <= 1:
        return ocsf.Severity.INFORMATIONAL
    elif nozomi_severity <= 3:
        return ocsf.Severity.LOW
    elif nozomi_severity <= 5:
        return ocsf.Severity.MEDIUM
    elif nozomi_severity <= 7:
        return ocsf.Severity.HIGH
    else:  # 8-10
        return ocsf.Severity.CRITICAL


def map_nozomi_status_to_ocsf(nozomi_status: str, is_acknowledged: bool = False) -> ocsf.DetectionStatus:
    """
    Map Nozomi Vantage alert status to OCSF DetectionStatus.

    Args:
        nozomi_status: Nozomi alert status ("open", "closed", etc.)
        is_acknowledged: Whether the alert is acknowledged

    Returns:
        OCSF DetectionStatus enum value
    """
    # Handle acknowledgment status
    if is_acknowledged and nozomi_status != "closed":
        return ocsf.DetectionStatus.IN_PROGRESS

    # Map status values
    status_mapping = {
        "open": ocsf.DetectionStatus.NEW,
        "closed": ocsf.DetectionStatus.RESOLVED,
    }

    return status_mapping.get(nozomi_status, ocsf.DetectionStatus.UNKNOWN)


def extract_mitre_attacks(alert_data: dict) -> List[ocsf.MitreAttack]:
    """
    Extract MITRE ATT&CK techniques from both ICS and Enterprise frameworks.

    Args:
        alert_data: Alert data containing MITRE information

    Returns:
        List of OCSF MitreAttack objects
    """
    attacks = []
    properties = alert_data.get("properties", {})

    # Extract ICS techniques
    ics_mitre = properties.get("mitre_attack_for_ics", {})
    ics_techniques = ics_mitre.get("techniques", [])

    for technique in ics_techniques:
        attack = ocsf.MitreAttack(
            technique=ocsf.Technique(
                uid=technique.get("id"),
                name=technique.get("name"),
            ),
            tactic=ocsf.Tactic(
                name=technique.get("tactic"),
            ),
        )
        attacks.append(attack)

    # Extract Enterprise techniques
    enterprise_mitre = properties.get("mitre_attack_enterprise", {})
    enterprise_techniques = enterprise_mitre.get("techniques", [])

    for technique in enterprise_techniques:
        attack = ocsf.MitreAttack(
            technique=ocsf.Technique(
                uid=technique.get("id"),
                name=technique.get("name"),
            ),
            tactic=ocsf.Tactic(
                name=technique.get("tactic"),
            ),
        )
        attacks.append(attack)

    return attacks


def create_evidence_artifacts(alert_data: dict) -> List[ocsf.EvidenceArtifacts]:
    """
    Create evidence artifacts for source and destination endpoints.

    Args:
        alert_data: Alert data containing endpoint information

    Returns:
        List of OCSF EvidenceArtifacts objects
    """
    evidences = []

    # Create source endpoint evidence
    if alert_data.get("ip_src") or alert_data.get("id_src"):
        src_endpoint = ocsf.NetworkEndpoint(
            uid=alert_data.get("id_src"),
            ip=alert_data.get("ip_src"),
            mac=alert_data.get("mac_src"),
            port=alert_data.get("port_src"),
            zone=alert_data.get("zone_src"),
            type=", ".join(alert_data.get("types_src", [])) if alert_data.get("types_src") else None,
        )

        src_evidence = ocsf.EvidenceArtifacts(
            src_endpoint=src_endpoint,
        )
        evidences.append(src_evidence)

    # Create destination endpoint evidence
    if alert_data.get("ip_dst") or alert_data.get("id_dst"):
        dst_endpoint = ocsf.NetworkEndpoint(
            uid=alert_data.get("id_dst"),
            ip=alert_data.get("ip_dst"),
            mac=alert_data.get("mac_dst"),
            port=alert_data.get("port_dst"),
            zone=alert_data.get("zone_dst"),
            type=", ".join(alert_data.get("types_dst", [])) if alert_data.get("types_dst") else None,
        )

        dst_evidence = ocsf.EvidenceArtifacts(
            dst_endpoint=dst_endpoint,
        )
        evidences.append(dst_evidence)

    return evidences


def create_enrichments(alert_data: dict) -> List[ocsf.Enrichment]:
    """
    Create enrichment objects for additional alert data.

    Args:
        alert_data: Alert data containing enrichment information

    Returns:
        List of OCSF Enrichment objects
    """
    enrichments = []

    # Add protocol enrichment
    if alert_data.get("protocol"):
        protocol_enrichment = ocsf.Enrichment(
            name="protocol",
            type="application_protocol",
            value=alert_data["protocol"],
        )
        enrichments.append(protocol_enrichment)

    # Add BPF filter enrichment
    if alert_data.get("bpf_filter"):
        bpf_enrichment = ocsf.Enrichment(
            name="bpf_filter",
            type="packet_filter",
            value=alert_data["bpf_filter"],
        )
        enrichments.append(bpf_enrichment)

    # Add source roles enrichment
    if alert_data.get("src_roles"):
        src_roles_enrichment = ocsf.Enrichment(
            name="src_roles",
            type="asset_roles",
            data={"roles": alert_data["src_roles"]},
        )
        enrichments.append(src_roles_enrichment)

    # Add destination roles enrichment
    if alert_data.get("dst_roles"):
        dst_roles_enrichment = ocsf.Enrichment(
            name="dst_roles",
            type="asset_roles",
            data={"roles": alert_data["dst_roles"]},
        )
        enrichments.append(dst_roles_enrichment)

    return enrichments


def convert_epoch_ms_to_datetime(epoch_ms: int) -> Optional[datetime]:
    """
    Convert epoch milliseconds to datetime object.

    Args:
        epoch_ms: Epoch time in milliseconds

    Returns:
        datetime object or None if conversion fails
    """
    if not epoch_ms or epoch_ms == 0:
        return None

    try:
        return datetime.fromtimestamp(epoch_ms / 1000, tz=timezone.utc)
    except (ValueError, OSError):
        return None


def convert_to_ocsf(alert_data: dict) -> ocsf.DetectionFinding:
    """
    Convert Nozomi Vantage alert data to OCSF DetectionFinding.

    Args:
        alert_data: Raw alert data from Nozomi Vantage API

    Returns:
        OCSF DetectionFinding object
    """
    # Extract basic alert information
    alert_id = alert_data.get("id")
    alert_name = alert_data.get("name", "")
    description = alert_data.get("description", "")
    severity = alert_data.get("severity", 0)
    is_incident = alert_data.get("is_incident", False)
    status = alert_data.get("status", "open")
    is_acknowledged = alert_data.get("ack", False)
    risk_score = alert_data.get("risk", 0)
    counter = alert_data.get("counter", 1)

    # Convert timestamps
    alert_time = convert_epoch_ms_to_datetime(alert_data.get("time"))
    created_time = convert_epoch_ms_to_datetime(alert_data.get("created_time"))
    closed_time = convert_epoch_ms_to_datetime(alert_data.get("closed_time"))

    # Map severity and status
    ocsf_severity = map_nozomi_severity_to_ocsf(severity, is_incident)
    ocsf_status = map_nozomi_status_to_ocsf(status, is_acknowledged)

    # Extract MITRE ATT&CK information
    attacks = extract_mitre_attacks(alert_data)

    # Create evidence artifacts
    evidences = create_evidence_artifacts(alert_data)

    # Create enrichments
    enrichments = create_enrichments(alert_data)

    # Build product information
    properties = alert_data.get("properties", {})
    feature_name = properties.get("raised_by")
    product = ocsf.Product(
        name="Nozomi Vantage",
        vendor_name="Nozomi Networks",
        version=properties.get("n2os_version") or "Unknown",
        feature=ocsf.Feature(name=feature_name) if feature_name else None,
    )

    # Build device information
    device = ocsf.Device(
        hostname=alert_data.get("appliance_host"),
    )

    # Build metadata
    metadata = ocsf.Metadata(
        uid=alert_id,
        correlation_uid=alert_id or "unknown",
        event_code=alert_data.get("type_name") or "nozomi_alert",
        product=product,
        profiles=[
            ocsf.Profile.DATETIME,
            ocsf.Profile.SECURITY_CONTROL,
        ],
    )

    # Build finding information
    finding_info = ocsf.FindingInformation(
        title=alert_name,
        desc=description,
        types=[alert_data.get("type_name")] if alert_data.get("type_name") else [],
        attacks=attacks if attacks else None,
        related_events_count=counter,
        created_time_dt=created_time,
    )

    # Build connection info if transport protocol is available
    connection_info = None
    if alert_data.get("transport_protocol"):
        connection_info = ocsf.NetworkConnectionInfo(
            protocol_name=alert_data["transport_protocol"],
        )

    # Ensure we have a valid time_dt (required field)
    event_time = alert_time or created_time
    if not event_time:
        # Fallback to current time if no timestamp is available
        event_time = datetime.now(timezone.utc)

    return ocsf.DetectionFinding(
        activity=ocsf.DetectionActivity.CREATE,
        severity=ocsf_severity,
        status=ocsf_status,
        time_dt=event_time,
        created_time_dt=created_time or event_time,
        end_time_dt=closed_time,
        message=description,
        risk_score=risk_score,
        metadata=metadata,
        finding_info=finding_info,
        evidences=evidences if evidences else [],
        enrichments=enrichments if enrichments else [],
        device=device,
        connection_info=connection_info,
    )


def normalize_event(alert_data: dict) -> Event:
    """
    Normalize a Nozomi Vantage alert to the Event structure.

    Args:
        alert_data: Raw alert data from Nozomi Vantage API

    Returns:
        Event: Normalized event with OCSF data
    """
    # Handle both API response structure and test data structure
    # API returns: {"id": "...", "attributes": {...}}
    # Test data might have: {"data": {"id": "...", "attributes": {...}}}
    if "data" in alert_data:
        # Test data structure
        data = alert_data.get("data", {})
        attributes = data.get("attributes", {})
        alert_id = attributes.get("id") or data.get("id")
    else:
        # API response structure
        attributes = alert_data.get("attributes", {})
        alert_id = attributes.get("id") or alert_data.get("id")

    # Ensure we have a valid alert_id
    if not alert_id:
        raise ValueError("Alert ID is required but not found in alert data")

    # Convert to OCSF
    ocsf_data = convert_to_ocsf(attributes)

    # Extract timestamp
    event_timestamp = convert_epoch_ms_to_datetime(attributes.get("time"))

    # Ensure we have a valid event timestamp
    if not event_timestamp:
        # Fallback to current time if no timestamp is available
        event_timestamp = datetime.now(timezone.utc)

    # Build vendor references
    vendor_item_ref = VendorRefExtended(
        id=alert_id,
        title=attributes.get("name", ""),
        created=convert_epoch_ms_to_datetime(attributes.get("created_time")),
    )

    # Build IOC information
    ioc = EventIOCInfo(
        external_id=attributes.get("type_id", ""),
        external_name=attributes.get("type_name", ""),
        has_ioc_definition=False,
        mitre_techniques=None,  # MITRE techniques are included in OCSF data
    )

    return Event(
        event_timestamp=event_timestamp,
        raw_event=alert_data,
        ocsf=ocsf_data,
        vendor_item_ref=vendor_item_ref,
        vendor_group_ref=None,
        ioc=ioc,
    )


class NozomiVantageV1EventSync(EventSync):
    """Event sync action for Nozomi Vantage alerts."""

    name = "Fetch Events Framework for Nozomi Vantage"

    @normalize(normalize_event)
    def execute(self, args: EventSyncArgs, **kwargs) -> Generator[Event, None, None]:
        """
        Execute the event sync to fetch alerts from Nozomi Vantage.

        Args:
            args: Event sync arguments containing time range and other parameters
            **kwargs: Additional keyword arguments

        Yields:
            Event: Normalized events from Nozomi Vantage alerts
        """
        api: NozomiVantageV1Api = self.integration.get_api()

        # Get bookmark for incremental sync
        bookmark: NozomiVantageV1EventSyncBookmark = self.integration.bookmarks.get(
            "event_sync", NozomiVantageV1EventSyncBookmark()
        )

        # Determine time range for fetching alerts
        since = args.start_time
        if bookmark.last_alert_time and (not since or bookmark.last_alert_time > since):
            since = bookmark.last_alert_time

        until = args.end_time

        logger.info(
            f"Fetching Nozomi Vantage alerts from {since} to {until}",
            extra={
                "start_time": since.isoformat() if since else None,
                "end_time": until.isoformat() if until else None,
                "last_bookmark_time": bookmark.last_alert_time.isoformat() if bookmark.last_alert_time else None,
            }
        )

        # Track the latest alert time for bookmark updates
        latest_alert_time = bookmark.last_alert_time
        processed_alert_ids = set()
        last_processed_alert_id = None

        try:
            # Fetch alerts using pagination
            for alert in api.paginate_alerts(since=since, until=until):
                alert_attributes = alert.get("attributes", {})
                alert_id = alert_attributes.get("id") or alert.get("id")
                alert_time = convert_epoch_ms_to_datetime(alert_attributes.get("time"))

                # Skip if we've already processed this alert (deduplication)
                if alert_id in processed_alert_ids:
                    continue

                # Skip if this alert is older than our bookmark
                if (bookmark.last_alert_id == alert_id and
                    bookmark.last_alert_time and alert_time and
                    alert_time <= bookmark.last_alert_time):
                    continue

                processed_alert_ids.add(alert_id)

                # Update latest alert time and ID for bookmark
                if alert_time and (not latest_alert_time or alert_time > latest_alert_time):
                    latest_alert_time = alert_time
                    last_processed_alert_id = alert_id

                logger.debug(f"Processing alert {alert_id} from {alert_time}")

                yield alert

        except Exception as e:
            logger.error(f"Error fetching alerts from Nozomi Vantage: {e}")
            raise

        # Update bookmark with latest processed alert time
        if latest_alert_time:
            bookmark.last_alert_time = latest_alert_time
            if last_processed_alert_id:
                # Store the last processed alert ID for deduplication
                bookmark.last_alert_id = last_processed_alert_id

            self.integration.bookmarks["event_sync"] = bookmark

            logger.info(
                f"Updated bookmark to {latest_alert_time.isoformat()}",
                extra={
                    "processed_alerts_count": len(processed_alert_ids),
                    "latest_alert_time": latest_alert_time.isoformat(),
                }
            )

    def get_permission_checks(self, *args, **kwargs):
        """Return permission checks required for this action."""
        # No specific permission checks defined for Nozomi Vantage yet
        # This would be implemented based on the API's permission model
        return []
